<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <h2>API Connection Test</h2>
        <p>Test all API endpoints to verify connectivity</p>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" md="6">
        <v-card class="mb-4" :color="results.main ? 'success' : (results.main === false ? 'error' : '')">
          <v-card-title>Main API Test</v-card-title>
          <v-card-text>
            <p>Endpoint: /api/ (192.168.1.85)</p>
            <v-btn @click="testMainAPI" :loading="loading.main" color="primary">
              Test Main API
            </v-btn>
            <div v-if="results.main !== null" class="mt-3">
              <strong>Status:</strong> {{ results.main ? 'Success' : 'Failed' }}<br>
              <strong>Message:</strong> {{ messages.main }}<br>
              <small v-if="responses.main">Response: {{ JSON.stringify(responses.main, null, 2) }}</small>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="6">
        <v-card class="mb-4" :color="results.shwethe ? 'success' : (results.shwethe === false ? 'error' : '')">
          <v-card-title>Shwethe API Test</v-card-title>
          <v-card-text>
            <p>Endpoint: /api-shwethe/ (pv-api.shwethe.com)</p>
            <v-btn @click="testShwetheAPI" :loading="loading.shwethe" color="primary">
              Test Shwethe API
            </v-btn>
            <div v-if="results.shwethe !== null" class="mt-3">
              <strong>Status:</strong> {{ results.shwethe ? 'Success' : 'Failed' }}<br>
              <strong>Message:</strong> {{ messages.shwethe }}<br>
              <small v-if="responses.shwethe">Response: {{ JSON.stringify(responses.shwethe, null, 2) }}</small>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="6">
        <v-card class="mb-4" :color="results.test ? 'success' : (results.test === false ? 'error' : '')">
          <v-card-title>Test API</v-card-title>
          <v-card-text>
            <p>Endpoint: /api-test/ (192.168.1.39:8080)</p>
            <v-btn @click="testTestAPI" :loading="loading.test" color="primary">
              Test API
            </v-btn>
            <div v-if="results.test !== null" class="mt-3">
              <strong>Status:</strong> {{ results.test ? 'Success' : 'Failed' }}<br>
              <strong>Message:</strong> {{ messages.test }}<br>
              <small v-if="responses.test">Response: {{ JSON.stringify(responses.test, null, 2) }}</small>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="6">
        <v-card class="mb-4" :color="results.petrol ? 'success' : (results.petrol === false ? 'error' : '')">
          <v-card-title>Petrol API Test</v-card-title>
          <v-card-text>
            <p>Endpoint: /api-petrol/ (192.168.1.11:8222)</p>
            <v-btn @click="testPetrolAPI" :loading="loading.petrol" color="primary">
              Test Petrol API
            </v-btn>
            <div v-if="results.petrol !== null" class="mt-3">
              <strong>Status:</strong> {{ results.petrol ? 'Success' : 'Failed' }}<br>
              <strong>Message:</strong> {{ messages.petrol }}<br>
              <small v-if="responses.petrol">Response: {{ JSON.stringify(responses.petrol, null, 2) }}</small>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import {
  HTTP,
  HTTP_TEST,
  HTTP_shwethe_n,
  PetrolProduct
} from "@/plugins/http";

export default {
  name: "ApiTest",
  data() {
    return {
      loading: {
        main: false,
        shwethe: false,
        test: false,
        petrol: false
      },
      results: {
        main: null,
        shwethe: null,
        test: null,
        petrol: null
      },
      messages: {
        main: '',
        shwethe: '',
        test: '',
        petrol: ''
      },
      responses: {
        main: null,
        shwethe: null,
        test: null,
        petrol: null
      }
    };
  },
  methods: {
    async testMainAPI() {
      this.loading.main = true;
      try {
        const response = await HTTP.get('test');
        this.setResult('main', true, 'Connection successful!', response.data);
      } catch (error) {
        this.setResult('main', false, error.message || 'Connection failed');
      }
      this.loading.main = false;
    },

    async testShwetheAPI() {
      this.loading.shwethe = true;
      try {
        const response = await HTTP_shwethe_n.get('test');
        this.setResult('shwethe', true, 'Connection successful!', response.data);
      } catch (error) {
        this.setResult('shwethe', false, error.message || 'Connection failed');
      }
      this.loading.shwethe = false;
    },

    async testTestAPI() {
      this.loading.test = true;
      try {
        const response = await HTTP_TEST.get('mongo');
        this.setResult('test', true, 'Connection successful!', response.data);
      } catch (error) {
        this.setResult('test', false, error.message || 'Connection failed');
      }
      this.loading.test = false;
    },

    async testPetrolAPI() {
      this.loading.petrol = true;
      try {
        const response = await PetrolProduct.get('');
        this.setResult('petrol', true, 'Connection successful!', response.data);
      } catch (error) {
        this.setResult('petrol', false, error.message || 'Connection failed');
      }
      this.loading.petrol = false;
    },

    setResult(api, success, message, data = null) {
      this.results[api] = success;
      this.messages[api] = message;
      this.responses[api] = data;
    }
  }
};
</script>
